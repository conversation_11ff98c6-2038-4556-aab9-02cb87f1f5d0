// 云函数：data-cleaner
// 用于清理和重置数据库
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 日志记录函数
function logInfo(message, data = null) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
}

function logError(message, error = null) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action = 'clear_all', confirm = false } = event;
  
  logInfo('数据清理云函数被调用', { action, confirm });
  
  try {
    switch (action) {
      case 'clear_all':
        if (!confirm) {
          return {
            success: false,
            error: '需要确认参数 confirm: true',
            message: '请使用 { action: "clear_all", confirm: true } 来确认清空操作'
          };
        }
        return await clearAllData();
      case 'count':
        return await countRecords();
      case 'clear_batch':
        return await clearBatchData(event.batchSize || 10);
      default:
        throw new Error(`未知的操作类型: ${action}`);
    }
  } catch (error) {
    logError('数据清理失败', {
      message: error.message,
      stack: error.stack,
      event: event
    });
    
    return {
      success: false,
      error: error.message,
      errorCode: error.errCode || 'CLEANER_ERROR',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 清空所有数据
 */
async function clearAllData() {
  logInfo('开始清空所有数据');
  
  let deletedCount = 0;
  let errorCount = 0;
  const errors = [];
  
  try {
    // 分批获取和删除数据
    let hasMore = true;
    let batchCount = 0;
    
    while (hasMore && batchCount < 100) { // 最多100批次，防止无限循环
      batchCount++;
      logInfo(`执行第 ${batchCount} 批次删除`);
      
      // 获取一批记录的ID
      const batch = await db.collection('knowledge_base')
        .field({ _id: true })
        .limit(20)
        .get();
      
      if (batch.data.length === 0) {
        hasMore = false;
        logInfo('没有更多数据需要删除');
        break;
      }
      
      logInfo(`获取到 ${batch.data.length} 条记录准备删除`);
      
      // 逐个删除记录
      for (const item of batch.data) {
        try {
          await db.collection('knowledge_base').doc(item._id).remove();
          deletedCount++;
          
          if (deletedCount % 5 === 0) {
            logInfo(`已删除 ${deletedCount} 条记录`);
          }
        } catch (deleteError) {
          errorCount++;
          errors.push({
            id: item._id,
            error: deleteError.message
          });
          logError(`删除记录失败`, { id: item._id, error: deleteError.message });
        }
      }
      
      // 短暂延迟，避免操作过快
      await new Promise(resolve => setTimeout(resolve, 200));
    }
    
    // 验证清空结果
    const finalCount = await db.collection('knowledge_base').count();
    
    logInfo('清空操作完成', {
      deletedCount,
      errorCount,
      remainingCount: finalCount.total
    });
    
    return {
      success: true,
      action: 'clear_all',
      results: {
        deleted_count: deletedCount,
        error_count: errorCount,
        remaining_count: finalCount.total,
        batch_count: batchCount,
        errors: errors.slice(0, 10) // 只返回前10个错误
      },
      message: finalCount.total === 0 ? '数据库已完全清空' : `还剩余 ${finalCount.total} 条记录`,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    logError('清空过程中出现严重错误', error);
    throw error;
  }
}

/**
 * 统计记录数量
 */
async function countRecords() {
  logInfo('统计记录数量');
  
  try {
    const countResult = await db.collection('knowledge_base').count();
    
    return {
      success: true,
      action: 'count',
      total: countResult.total,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logError('统计失败', error);
    throw error;
  }
}

/**
 * 分批清理数据
 */
async function clearBatchData(batchSize = 10) {
  logInfo(`分批清理数据，批次大小: ${batchSize}`);
  
  try {
    const batch = await db.collection('knowledge_base')
      .field({ _id: true })
      .limit(batchSize)
      .get();
    
    if (batch.data.length === 0) {
      return {
        success: true,
        action: 'clear_batch',
        deleted_count: 0,
        message: '没有数据需要删除',
        timestamp: new Date().toISOString()
      };
    }
    
    let deletedCount = 0;
    const errors = [];
    
    for (const item of batch.data) {
      try {
        await db.collection('knowledge_base').doc(item._id).remove();
        deletedCount++;
      } catch (deleteError) {
        errors.push({
          id: item._id,
          error: deleteError.message
        });
      }
    }
    
    const remainingCount = await db.collection('knowledge_base').count();
    
    return {
      success: true,
      action: 'clear_batch',
      deleted_count: deletedCount,
      error_count: errors.length,
      remaining_count: remainingCount.total,
      errors: errors,
      has_more: remainingCount.total > 0,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    logError('分批清理失败', error);
    throw error;
  }
}
