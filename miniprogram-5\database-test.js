// 数据库连接测试脚本
// 在微信开发者工具控制台中运行

console.log('=== 数据库连接测试开始 ===');

// 测试云开发初始化状态
function testCloudInit() {
  console.log('\n1. 测试云开发初始化状态:');
  
  if (typeof wx === 'undefined') {
    console.log('❌ wx对象未定义，请在微信开发者工具中运行');
    return false;
  }
  
  if (!wx.cloud) {
    console.log('❌ wx.cloud未定义，云开发未初始化');
    return false;
  }
  
  console.log('✅ wx.cloud已定义');
  return true;
}

// 测试数据库连接
async function testDatabaseConnection() {
  console.log('\n2. 测试数据库连接:');
  
  try {
    const db = wx.cloud.database();
    console.log('✅ 数据库对象创建成功');
    
    // 测试基本查询
    const result = await db.collection('knowledge_base').count();
    console.log('✅ 数据库查询成功');
    console.log('📊 知识库记录总数:', result.total);
    
    return result.total;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error);
    console.log('错误详情:', {
      errCode: error.errCode,
      errMsg: error.errMsg,
      message: error.message
    });
    return -1;
  }
}

// 测试云函数调用
async function testCloudFunction() {
  console.log('\n3. 测试云函数调用:');
  
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: {
        action: 'count'
      }
    });
    
    console.log('✅ 云函数调用成功');
    console.log('📊 云函数返回结果:', result.result);
    
    return result.result;
  } catch (error) {
    console.log('❌ 云函数调用失败:', error);
    console.log('错误详情:', {
      errCode: error.errCode,
      errMsg: error.errMsg,
      message: error.message
    });
    return null;
  }
}

// 测试数据库权限
async function testDatabasePermissions() {
  console.log('\n4. 测试数据库权限:');
  
  try {
    const db = wx.cloud.database();
    
    // 测试读权限
    const readResult = await db.collection('knowledge_base').limit(1).get();
    console.log('✅ 数据库读权限正常');
    console.log('📖 示例数据:', readResult.data.length > 0 ? '有数据' : '无数据');
    
    // 测试写权限（添加测试记录）
    const testDoc = {
      title: '数据库连接测试',
      content: '这是一个测试记录',
      created_at: new Date(),
      test_record: true
    };
    
    const writeResult = await db.collection('knowledge_base').add({
      data: testDoc
    });
    console.log('✅ 数据库写权限正常');
    console.log('📝 测试记录ID:', writeResult._id);
    
    // 删除测试记录
    await db.collection('knowledge_base').doc(writeResult._id).remove();
    console.log('✅ 数据库删除权限正常');
    
    return true;
  } catch (error) {
    console.log('❌ 数据库权限测试失败:', error);
    console.log('错误详情:', {
      errCode: error.errCode,
      errMsg: error.errMsg,
      message: error.message
    });
    return false;
  }
}

// 检查环境配置
function checkEnvironmentConfig() {
  console.log('\n5. 检查环境配置:');
  
  // 检查全局数据
  const app = getApp();
  if (app && app.globalData) {
    console.log('✅ 应用全局数据存在');
    console.log('🌍 环境ID:', app.globalData.env);
    console.log('👤 用户信息:', app.globalData.userInfo ? '已设置' : '未设置');
    console.log('🔑 OpenID:', app.globalData.openid ? '已设置' : '未设置');
  } else {
    console.log('❌ 应用全局数据不存在');
  }
}

// 主测试函数
async function runDatabaseTest() {
  console.log('开始执行数据库连接测试...\n');
  
  // 1. 测试云开发初始化
  const cloudInitOk = testCloudInit();
  if (!cloudInitOk) {
    console.log('\n❌ 云开发初始化失败，无法继续测试');
    return;
  }
  
  // 2. 检查环境配置
  checkEnvironmentConfig();
  
  // 3. 测试数据库连接
  const recordCount = await testDatabaseConnection();
  
  // 4. 测试云函数调用
  const cloudFunctionResult = await testCloudFunction();
  
  // 5. 测试数据库权限
  const permissionsOk = await testDatabasePermissions();
  
  // 总结测试结果
  console.log('\n=== 测试结果总结 ===');
  console.log('云开发初始化:', cloudInitOk ? '✅ 正常' : '❌ 失败');
  console.log('数据库连接:', recordCount >= 0 ? '✅ 正常' : '❌ 失败');
  console.log('云函数调用:', cloudFunctionResult ? '✅ 正常' : '❌ 失败');
  console.log('数据库权限:', permissionsOk ? '✅ 正常' : '❌ 失败');
  console.log('知识库记录数:', recordCount);
  
  if (recordCount === 0) {
    console.log('\n⚠️ 警告: 知识库中没有数据！');
    console.log('这可能是数据丢失的原因。建议：');
    console.log('1. 检查数据是否真的上传成功');
    console.log('2. 检查数据库集合名称是否正确');
    console.log('3. 检查数据库权限设置');
  }
  
  console.log('\n=== 测试完成 ===');
}

// 导出测试函数，可以在控制台中调用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runDatabaseTest };
} else {
  // 在浏览器环境中直接运行
  window.runDatabaseTest = runDatabaseTest;
  console.log('数据库测试脚本已加载，请在控制台中运行: runDatabaseTest()');
}
