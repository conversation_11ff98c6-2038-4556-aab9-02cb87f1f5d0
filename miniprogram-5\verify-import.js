// 验证知识库导入完整性检查工具
// 检查云开发数据库中的数据是否完整导入

console.log('=== 知识库导入完整性验证工具 ===');

// 预期的数据统计
const EXPECTED_STATS = {
  totalRecords: 427,
  totalBatches: 44,
  batchFiles: [
    'batch-001.json', 'batch-002.json', 'batch-003.json', 'batch-004.json', 'batch-005.json',
    'batch-006.json', 'batch-007.json', 'batch-008.json', 'batch-009.json', 'batch-010.json',
    'batch-011.json', 'batch-012.json', 'batch-013.json', 'batch-014.json', 'batch-015.json',
    'batch-016.json', 'batch-017.json', 'batch-018.json', 'batch-019.json', 'batch-020.json',
    'batch-021.json', 'batch-022.json', 'batch-023.json', 'batch-024a.json', 'batch-024b.json',
    'batch-024c.json', 'batch-025.json', 'batch-026.json', 'batch-027.json', 'batch-028.json',
    'batch-029.json', 'batch-030.json', 'batch-031.json', 'batch-032.json', 'batch-033.json',
    'batch-034.json', 'batch-035.json', 'batch-036.json', 'batch-037.json', 'batch-038.json',
    'batch-039.json', 'batch-040.json', 'batch-041.json', 'batch-042.json', 'batch-043.json',
    'batch-044.json'
  ]
};

// 在小程序中运行的验证函数
function createVerificationScript() {
  return `
// === 在微信开发者工具控制台中运行此脚本 ===

console.log('🔍 开始验证知识库导入完整性...');

// 1. 检查总记录数
async function checkTotalRecords() {
  try {
    const countResult = await wx.cloud.database().collection('knowledge_base').count();
    console.log('📊 数据库记录总数:', countResult.total);
    console.log('📋 预期记录总数:', 427);
    
    if (countResult.total === 427) {
      console.log('✅ 记录总数正确！');
      return true;
    } else {
      console.log('❌ 记录总数不匹配！');
      console.log('📉 缺少记录数:', 427 - countResult.total);
      return false;
    }
  } catch (error) {
    console.error('❌ 检查总记录数失败:', error);
    return false;
  }
}

// 2. 检查批次分布
async function checkBatchDistribution() {
  try {
    console.log('\\n🔍 检查批次分布...');
    
    const batchStats = {};
    let totalChecked = 0;
    
    // 分页查询所有记录
    let hasMore = true;
    let lastId = null;
    
    while (hasMore) {
      let query = wx.cloud.database().collection('knowledge_base')
        .field({ batch_info: true, _id: true })
        .limit(100);
      
      if (lastId) {
        query = query.where({
          _id: wx.cloud.database().command.gt(lastId)
        });
      }
      
      const result = await query.get();
      
      if (result.data.length === 0) {
        hasMore = false;
        break;
      }
      
      result.data.forEach(record => {
        if (record.batch_info && record.batch_info.batch_number) {
          const batchNum = record.batch_info.batch_number;
          batchStats[batchNum] = (batchStats[batchNum] || 0) + 1;
          totalChecked++;
        }
        lastId = record._id;
      });
      
      if (result.data.length < 100) {
        hasMore = false;
      }
    }
    
    console.log('📊 批次统计结果:');
    console.log('总检查记录数:', totalChecked);
    
    // 显示每个批次的记录数
    const sortedBatches = Object.keys(batchStats).sort((a, b) => parseInt(a) - parseInt(b));
    let allBatchesPresent = true;
    
    for (let i = 1; i <= 44; i++) {
      const count = batchStats[i] || 0;
      if (count === 0) {
        console.log(\`❌ 批次 \${i}: 0 条记录 (缺失)\`);
        allBatchesPresent = false;
      } else {
        console.log(\`✅ 批次 \${i}: \${count} 条记录\`);
      }
    }
    
    return allBatchesPresent;
  } catch (error) {
    console.error('❌ 检查批次分布失败:', error);
    return false;
  }
}

// 3. 检查数据质量
async function checkDataQuality() {
  try {
    console.log('\\n🔍 检查数据质量...');
    
    // 检查必要字段
    const sampleResult = await wx.cloud.database().collection('knowledge_base')
      .limit(10)
      .get();
    
    if (sampleResult.data.length === 0) {
      console.log('❌ 没有找到任何记录！');
      return false;
    }
    
    let qualityIssues = 0;
    
    sampleResult.data.forEach((record, index) => {
      console.log(\`\\n📋 检查样本记录 \${index + 1}:\`);
      
      // 检查必要字段
      const requiredFields = ['title', 'content', 'file_path', 'category', 'created_at', 'batch_info'];
      requiredFields.forEach(field => {
        if (!record[field]) {
          console.log(\`❌ 缺少字段: \${field}\`);
          qualityIssues++;
        } else {
          console.log(\`✅ 字段 \${field}: 存在\`);
        }
      });
      
      // 检查内容长度
      if (record.content && record.content.length > 0) {
        console.log(\`📏 内容长度: \${record.content.length} 字符\`);
      } else {
        console.log('❌ 内容为空');
        qualityIssues++;
      }
      
      // 检查批次信息
      if (record.batch_info) {
        console.log(\`📦 批次号: \${record.batch_info.batch_number}\`);
        console.log(\`📁 原始文件: \${record.batch_info.file_name}\`);
      }
    });
    
    console.log(\`\\n📊 数据质量检查完成，发现 \${qualityIssues} 个问题\`);
    return qualityIssues === 0;
  } catch (error) {
    console.error('❌ 检查数据质量失败:', error);
    return false;
  }
}

// 4. 检查特定文件是否存在
async function checkSpecificFiles() {
  try {
    console.log('\\n🔍 检查关键文件是否存在...');
    
    const keyFiles = [
      '第一册',
      '周易古今文全書',
      '紫微鬥數',
      '子平八字',
      '梅花易数'
    ];
    
    let foundFiles = 0;
    
    for (const fileName of keyFiles) {
      const result = await wx.cloud.database().collection('knowledge_base')
        .where({
          title: wx.cloud.database().RegExp({
            regexp: fileName,
            options: 'i'
          })
        })
        .limit(1)
        .get();
      
      if (result.data.length > 0) {
        console.log(\`✅ 找到关键文件: \${fileName}\`);
        foundFiles++;
      } else {
        console.log(\`❌ 未找到关键文件: \${fileName}\`);
      }
    }
    
    console.log(\`\\n📊 关键文件检查: \${foundFiles}/\${keyFiles.length} 个文件存在\`);
    return foundFiles === keyFiles.length;
  } catch (error) {
    console.error('❌ 检查特定文件失败:', error);
    return false;
  }
}

// 5. 生成完整报告
async function generateReport() {
  console.log('\\n📋 === 导入完整性验证报告 ===');
  
  const results = {
    totalRecords: await checkTotalRecords(),
    batchDistribution: await checkBatchDistribution(),
    dataQuality: await checkDataQuality(),
    keyFiles: await checkSpecificFiles()
  };
  
  console.log('\\n📊 === 验证结果汇总 ===');
  console.log('✅ 记录总数检查:', results.totalRecords ? '通过' : '失败');
  console.log('✅ 批次分布检查:', results.batchDistribution ? '通过' : '失败');
  console.log('✅ 数据质量检查:', results.dataQuality ? '通过' : '失败');
  console.log('✅ 关键文件检查:', results.keyFiles ? '通过' : '失败');
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('\\n🎉 恭喜！知识库导入完全成功！');
    console.log('📚 所有 427 条记录已正确导入');
    console.log('🔧 数据库已准备就绪，可以开始使用');
  } else {
    console.log('\\n⚠️ 发现问题，需要进一步检查');
    console.log('💡 建议重新导入失败的批次或检查导入过程');
  }
  
  return results;
}

// 执行完整验证
generateReport().then(results => {
  console.log('\\n🏁 验证完成！');
}).catch(error => {
  console.error('❌ 验证过程出错:', error);
});
`;
}

// 显示使用说明
console.log('\n📋 === 使用说明 ===');
console.log('');
console.log('🔧 步骤1: 复制验证脚本');
console.log('将下面生成的脚本复制到微信开发者工具的控制台中运行');
console.log('');
console.log('🔧 步骤2: 在控制台运行');
console.log('打开微信开发者工具 → 控制台 → 粘贴脚本 → 回车执行');
console.log('');
console.log('🔧 步骤3: 查看验证结果');
console.log('脚本会自动检查：');
console.log('- ✅ 总记录数是否为 427 条');
console.log('- ✅ 44 个批次是否都有数据');
console.log('- ✅ 数据字段是否完整');
console.log('- ✅ 关键文件是否存在');
console.log('');

// 生成验证脚本
const verificationScript = createVerificationScript();

console.log('📄 === 验证脚本 (复制到控制台运行) ===');
console.log('');
console.log(verificationScript);
console.log('');
console.log('📄 === 脚本结束 ===');

// 创建简化版本的快速检查
console.log('\n🚀 === 快速检查版本 ===');
console.log('如果只想快速检查记录总数，可以运行：');
console.log('');
console.log('wx.cloud.database().collection("knowledge_base").count().then(res => {');
console.log('  console.log("数据库记录总数:", res.total);');
console.log('  console.log("预期记录总数:", 427);');
console.log('  if (res.total === 427) {');
console.log('    console.log("✅ 导入完成！");');
console.log('  } else {');
console.log('    console.log("❌ 记录数不匹配，缺少:", 427 - res.total, "条");');
console.log('  }');
console.log('});');

console.log('\n🎯 验证工具已准备就绪！请按照说明在微信开发者工具中运行验证脚本。');
