// 数据分析脚本 - 详细分析数据库状态
console.log('=== 数据库详细分析 ===');

// 分析数据库中的数据
async function analyzeDatabase() {
  console.log('\n📊 开始分析数据库数据...');
  
  try {
    const db = wx.cloud.database();
    
    // 1. 获取总数
    const countResult = await db.collection('knowledge_base').count();
    console.log(`📈 总记录数: ${countResult.total}`);
    
    // 2. 获取所有记录的基本信息（不包含content字段）
    const allRecords = await db.collection('knowledge_base')
      .field({ 
        title: true, 
        category: true, 
        file_path: true, 
        file_size: true, 
        created_at: true,
        batch_info: true
      })
      .get();
    
    console.log(`📋 实际获取记录数: ${allRecords.data.length}`);
    
    // 3. 分析数据分布
    const categories = {};
    const batches = {};
    const dates = {};
    let totalSize = 0;
    
    allRecords.data.forEach(record => {
      // 分类统计
      const category = record.category || '未分类';
      categories[category] = (categories[category] || 0) + 1;
      
      // 批次统计
      if (record.batch_info && record.batch_info.original_index !== undefined) {
        const batchKey = `批次${Math.floor(record.batch_info.original_index / 10) + 1}`;
        batches[batchKey] = (batches[batchKey] || 0) + 1;
      }
      
      // 日期统计
      if (record.created_at) {
        const date = new Date(record.created_at).toDateString();
        dates[date] = (dates[date] || 0) + 1;
      }
      
      // 大小统计
      if (record.file_size) {
        totalSize += record.file_size;
      }
    });
    
    console.log('\n📊 数据分布分析:');
    console.log('分类分布:', categories);
    console.log('批次分布:', batches);
    console.log('日期分布:', dates);
    console.log(`总文件大小: ${Math.round(totalSize / 1024 / 1024 * 100) / 100} MB`);
    
    // 4. 显示最近的几条记录
    console.log('\n📝 最近的记录:');
    const recentRecords = allRecords.data
      .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
      .slice(0, 5);
    
    recentRecords.forEach((record, index) => {
      console.log(`${index + 1}. ${record.title} (${record.category}) - ${new Date(record.created_at).toLocaleString()}`);
    });
    
    // 5. 检查是否有测试记录
    const testRecords = allRecords.data.filter(record => 
      record.title && (
        record.title.includes('测试') || 
        record.title.includes('test') ||
        record.test_record === true
      )
    );
    
    if (testRecords.length > 0) {
      console.log(`\n🧪 发现 ${testRecords.length} 条测试记录`);
    }
    
    return {
      total: countResult.total,
      categories,
      batches,
      dates,
      totalSize,
      recentRecords: recentRecords.slice(0, 3)
    };
    
  } catch (error) {
    console.error('❌ 数据分析失败:', error);
    return null;
  }
}

// 检查批次文件状态
async function checkBatchStatus() {
  console.log('\n📦 检查批次上传状态...');
  
  try {
    const db = wx.cloud.database();
    
    // 查找有batch_info的记录
    const batchRecords = await db.collection('knowledge_base')
      .where({
        batch_info: db.command.exists(true)
      })
      .field({ batch_info: true, file_path: true })
      .get();
    
    console.log(`📊 有批次信息的记录: ${batchRecords.data.length}`);
    
    if (batchRecords.data.length > 0) {
      const batchNumbers = new Set();
      batchRecords.data.forEach(record => {
        if (record.batch_info && record.batch_info.original_index !== undefined) {
          const batchNum = Math.floor(record.batch_info.original_index / 10) + 1;
          batchNumbers.add(batchNum);
        }
      });
      
      console.log('已上传的批次:', Array.from(batchNumbers).sort((a, b) => a - b));
    }
    
    return batchRecords.data.length;
    
  } catch (error) {
    console.error('❌ 批次状态检查失败:', error);
    return 0;
  }
}

// 主分析函数
async function runDataAnalysis() {
  console.log('开始执行数据库详细分析...\n');
  
  const analysisResult = await analyzeDatabase();
  const batchStatus = await checkBatchStatus();
  
  console.log('\n=== 分析结论 ===');
  
  if (analysisResult && analysisResult.total > 0) {
    console.log('✅ 数据库中确实有数据');
    console.log(`📊 总记录数: ${analysisResult.total}`);
    
    if (analysisResult.total < 100) {
      console.log('⚠️ 记录数量较少，可能原因:');
      console.log('1. 只有部分批次上传成功');
      console.log('2. 之前的数据可能被清空过');
      console.log('3. 上传过程中出现了问题');
      
      console.log('\n💡 建议操作:');
      console.log('1. 检查是否需要重新上传剩余批次');
      console.log('2. 验证现有数据的完整性');
      console.log('3. 继续上传缺失的数据');
    } else {
      console.log('✅ 数据量正常');
    }
  } else {
    console.log('❌ 数据库中没有数据或分析失败');
  }
  
  console.log('\n=== 分析完成 ===');
  return analysisResult;
}

// 导出函数
window.runDataAnalysis = runDataAnalysis;
window.analyzeDatabase = analyzeDatabase;
window.checkBatchStatus = checkBatchStatus;

console.log('数据分析脚本已加载！');
console.log('请运行: runDataAnalysis()');
