// 云函数：data-validator
// 用于验证数据库数据完整性和连接状态
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 日志记录函数
function logInfo(message, data = null) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
}

function logError(message, error = null) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

function logWarning(message, data = null) {
  console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`, data || '');
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { action = 'validate' } = event;
  
  logInfo('数据验证云函数被调用', { action });
  
  try {
    switch (action) {
      case 'validate':
        return await validateDatabase();
      case 'health_check':
        return await healthCheck();
      case 'data_integrity':
        return await checkDataIntegrity();
      case 'collection_info':
        return await getCollectionInfo();
      default:
        throw new Error(`未知的操作类型: ${action}`);
    }
  } catch (error) {
    logError('数据验证失败', {
      message: error.message,
      stack: error.stack,
      event: event
    });
    
    return {
      success: false,
      error: error.message,
      errorCode: error.errCode || 'VALIDATION_ERROR',
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * 数据库基本验证
 */
async function validateDatabase() {
  logInfo('开始数据库基本验证');
  
  const results = {
    database_connection: false,
    collection_exists: false,
    data_count: 0,
    sample_data: null,
    permissions: {
      read: false,
      write: false,
      delete: false
    },
    issues: []
  };
  
  try {
    // 1. 测试数据库连接
    logInfo('测试数据库连接');
    const countResult = await db.collection('knowledge_base').count();
    results.database_connection = true;
    results.collection_exists = true;
    results.data_count = countResult.total;
    logInfo('数据库连接成功', { count: countResult.total });
    
    // 2. 测试读权限
    logInfo('测试读权限');
    const readResult = await db.collection('knowledge_base').limit(1).get();
    results.permissions.read = true;
    results.sample_data = readResult.data[0] || null;
    logInfo('读权限测试成功');
    
    // 3. 测试写权限
    logInfo('测试写权限');
    const testDoc = {
      title: '数据验证测试记录',
      content: '这是一个测试记录，用于验证写权限',
      created_at: new Date(),
      test_record: true,
      validation_id: `test_${Date.now()}`
    };
    
    const writeResult = await db.collection('knowledge_base').add({
      data: testDoc
    });
    results.permissions.write = true;
    logInfo('写权限测试成功', { id: writeResult._id });
    
    // 4. 测试删除权限
    logInfo('测试删除权限');
    await db.collection('knowledge_base').doc(writeResult._id).remove();
    results.permissions.delete = true;
    logInfo('删除权限测试成功');
    
    // 5. 检查数据完整性问题
    if (results.data_count === 0) {
      results.issues.push('数据库中没有数据');
    }
    
    if (!results.sample_data) {
      results.issues.push('无法获取示例数据');
    }
    
  } catch (error) {
    logError('数据库验证过程中出错', error);
    results.issues.push(`验证错误: ${error.message}`);
  }
  
  return {
    success: true,
    action: 'validate',
    results: results,
    timestamp: new Date().toISOString()
  };
}

/**
 * 健康检查
 */
async function healthCheck() {
  logInfo('开始健康检查');
  
  const health = {
    status: 'healthy',
    checks: {
      database: { status: 'unknown', message: '' },
      collection: { status: 'unknown', message: '' },
      data: { status: 'unknown', message: '' }
    },
    metrics: {
      response_time: 0,
      data_count: 0,
      last_updated: null
    }
  };
  
  const startTime = Date.now();
  
  try {
    // 数据库连接检查
    await db.collection('knowledge_base').count();
    health.checks.database = { status: 'healthy', message: '数据库连接正常' };
    
    // 集合存在检查
    const countResult = await db.collection('knowledge_base').count();
    health.checks.collection = { status: 'healthy', message: '集合存在且可访问' };
    health.metrics.data_count = countResult.total;
    
    // 数据检查
    if (countResult.total > 0) {
      const latestData = await db.collection('knowledge_base')
        .orderBy('created_at', 'desc')
        .limit(1)
        .get();
      
      health.checks.data = { status: 'healthy', message: '数据存在且可读取' };
      health.metrics.last_updated = latestData.data[0]?.created_at || null;
    } else {
      health.checks.data = { status: 'warning', message: '数据库中没有数据' };
      health.status = 'warning';
    }
    
  } catch (error) {
    logError('健康检查失败', error);
    health.status = 'unhealthy';
    health.checks.database = { status: 'error', message: error.message };
  }
  
  health.metrics.response_time = Date.now() - startTime;
  
  return {
    success: true,
    action: 'health_check',
    health: health,
    timestamp: new Date().toISOString()
  };
}

/**
 * 数据完整性检查
 */
async function checkDataIntegrity() {
  logInfo('开始数据完整性检查');
  
  const integrity = {
    total_records: 0,
    valid_records: 0,
    invalid_records: 0,
    missing_fields: [],
    duplicate_files: [],
    issues: []
  };
  
  try {
    // 获取所有记录进行检查
    const allData = await db.collection('knowledge_base').get();
    integrity.total_records = allData.data.length;
    
    const requiredFields = ['title', 'content', 'file_path'];
    const filePathSet = new Set();
    
    allData.data.forEach((record, index) => {
      let isValid = true;
      
      // 检查必需字段
      requiredFields.forEach(field => {
        if (!record[field] || record[field].trim() === '') {
          isValid = false;
          integrity.missing_fields.push({
            record_id: record._id,
            missing_field: field,
            index: index
          });
        }
      });
      
      // 检查重复文件路径
      if (record.file_path) {
        if (filePathSet.has(record.file_path)) {
          integrity.duplicate_files.push({
            file_path: record.file_path,
            record_id: record._id
          });
        } else {
          filePathSet.add(record.file_path);
        }
      }
      
      if (isValid) {
        integrity.valid_records++;
      } else {
        integrity.invalid_records++;
      }
    });
    
    // 生成问题报告
    if (integrity.missing_fields.length > 0) {
      integrity.issues.push(`发现 ${integrity.missing_fields.length} 个缺失字段的记录`);
    }
    
    if (integrity.duplicate_files.length > 0) {
      integrity.issues.push(`发现 ${integrity.duplicate_files.length} 个重复文件路径`);
    }
    
    if (integrity.invalid_records > 0) {
      integrity.issues.push(`发现 ${integrity.invalid_records} 个无效记录`);
    }
    
  } catch (error) {
    logError('数据完整性检查失败', error);
    integrity.issues.push(`检查失败: ${error.message}`);
  }
  
  return {
    success: true,
    action: 'data_integrity',
    integrity: integrity,
    timestamp: new Date().toISOString()
  };
}

/**
 * 获取集合信息
 */
async function getCollectionInfo() {
  logInfo('获取集合信息');
  
  const info = {
    collection_name: 'knowledge_base',
    total_records: 0,
    total_size: 0,
    categories: {},
    date_range: {
      earliest: null,
      latest: null
    },
    sample_records: []
  };
  
  try {
    // 获取总数
    const countResult = await db.collection('knowledge_base').count();
    info.total_records = countResult.total;
    
    if (countResult.total > 0) {
      // 获取示例记录
      const sampleResult = await db.collection('knowledge_base').limit(5).get();
      info.sample_records = sampleResult.data.map(record => ({
        id: record._id,
        title: record.title,
        category: record.category,
        file_size: record.file_size,
        created_at: record.created_at
      }));
      
      // 计算总大小和分类统计
      const allData = await db.collection('knowledge_base')
        .field({ file_size: true, category: true, created_at: true })
        .get();
      
      allData.data.forEach(record => {
        if (record.file_size) {
          info.total_size += record.file_size;
        }
        
        if (record.category) {
          info.categories[record.category] = (info.categories[record.category] || 0) + 1;
        }
        
        if (record.created_at) {
          const date = new Date(record.created_at);
          if (!info.date_range.earliest || date < new Date(info.date_range.earliest)) {
            info.date_range.earliest = record.created_at;
          }
          if (!info.date_range.latest || date > new Date(info.date_range.latest)) {
            info.date_range.latest = record.created_at;
          }
        }
      });
    }
    
  } catch (error) {
    logError('获取集合信息失败', error);
    throw error;
  }
  
  return {
    success: true,
    action: 'collection_info',
    info: info,
    timestamp: new Date().toISOString()
  };
}
