// 准备云开发控制台导入数据
// 将upload-batches中的JSON文件转换为标准导入格式

console.log('=== 准备云开发控制台导入数据 ===');

// 批次文件列表
const batchFiles = [
  'batch-001.json', 'batch-002.json', 'batch-003.json', 'batch-004.json', 'batch-005.json',
  'batch-006.json', 'batch-007.json', 'batch-008.json', 'batch-009.json', 'batch-010.json',
  'batch-011.json', 'batch-012.json', 'batch-013.json', 'batch-014.json', 'batch-015.json',
  'batch-016.json', 'batch-017.json', 'batch-018.json', 'batch-019.json', 'batch-020.json',
  'batch-021.json', 'batch-022.json', 'batch-023.json', 'batch-024a.json', 'batch-024b.json',
  'batch-024c.json', 'batch-025.json', 'batch-026.json', 'batch-027.json', 'batch-028.json',
  'batch-029.json', 'batch-030.json', 'batch-031.json', 'batch-032.json', 'batch-033.json',
  'batch-034.json', 'batch-035.json', 'batch-036.json', 'batch-037.json', 'batch-038.json',
  'batch-039.json', 'batch-040.json', 'batch-041.json', 'batch-042.json', 'batch-043.json',
  'batch-044.json'
];

// 转换单个批次数据为导入格式
function convertBatchToImportFormat(batchData) {
  if (!batchData || !batchData.data) {
    console.error('批次数据格式错误');
    return [];
  }
  
  const importRecords = [];
  
  batchData.data.forEach((fileData, index) => {
    const record = {
      title: fileData.title || '',
      content: fileData.content || '',
      file_path: fileData.filePath || '',
      category: fileData.category || 'other',
      author: fileData.author || '',
      dynasty: fileData.dynasty || '',
      file_size: fileData.content ? fileData.content.length : 0,
      created_at: new Date().toISOString(),
      batch_info: {
        batch_number: batchData.batchNumber,
        total_batches: batchData.totalBatches,
        original_index: index,
        file_name: batchData.files ? batchData.files[index] : `file_${index}`
      }
    };
    
    importRecords.push(record);
  });
  
  return importRecords;
}

// 处理所有批次数据
function processAllBatches() {
  console.log('\n📋 批次处理指南:');
  console.log('请按以下步骤操作：');
  console.log('\n1. 逐个复制批次文件内容');
  console.log('2. 调用 convertAndDownload(batchData, batchNumber)');
  console.log('3. 下载生成的JSON文件');
  console.log('4. 在云开发控制台导入');
  
  // 创建转换和下载函数
  window.convertAndDownload = function(batchData, batchNumber) {
    console.log(`\n🔄 转换批次 ${batchNumber} 数据...`);
    
    const importData = convertBatchToImportFormat(batchData);
    
    if (importData.length === 0) {
      console.error('❌ 转换失败，数据为空');
      return;
    }
    
    // 创建下载链接
    const jsonString = JSON.stringify(importData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    // 创建下载链接
    const a = document.createElement('a');
    a.href = url;
    a.download = `knowledge_base_batch_${batchNumber.toString().padStart(3, '0')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`✅ 批次 ${batchNumber} 转换完成，已下载: knowledge_base_batch_${batchNumber.toString().padStart(3, '0')}.json`);
    console.log(`📊 包含 ${importData.length} 条记录`);
    
    return importData;
  };
  
  // 创建合并所有批次的函数
  window.convertAllBatches = function(allBatchesData) {
    console.log('\n🔄 转换所有批次数据...');
    
    const allImportData = [];
    
    allBatchesData.forEach((batchData, index) => {
      const batchImportData = convertBatchToImportFormat(batchData);
      allImportData.push(...batchImportData);
      console.log(`✅ 批次 ${index + 1} 转换完成: ${batchImportData.length} 条记录`);
    });
    
    // 创建下载链接
    const jsonString = JSON.stringify(allImportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'knowledge_base_complete.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`🎉 所有批次转换完成！`);
    console.log(`📊 总计 ${allImportData.length} 条记录`);
    console.log(`📁 已下载: knowledge_base_complete.json`);
    
    return allImportData;
  };
}

// 显示使用指南
function showImportGuide() {
  console.log('\n📋 云开发控制台导入指南:');
  console.log('');
  console.log('🔧 方法1: 分批次导入');
  console.log('1. 复制 upload-batches/batch-001.json 内容到 batchData 变量');
  console.log('2. 运行: convertAndDownload(batchData, 1)');
  console.log('3. 重复步骤1-2，处理所有44个批次');
  console.log('4. 在云开发控制台逐个导入生成的JSON文件');
  console.log('');
  console.log('🚀 方法2: 一次性导入（推荐）');
  console.log('1. 将所有44个批次数据放入数组: [batch1Data, batch2Data, ...]');
  console.log('2. 运行: convertAllBatches(allBatchesData)');
  console.log('3. 在云开发控制台导入生成的 knowledge_base_complete.json');
  console.log('');
  console.log('📍 云开发控制台导入路径:');
  console.log('微信开发者工具 → 云开发 → 数据库 → knowledge_base → 导入');
  console.log('');
  console.log('⚠️ 导入注意事项:');
  console.log('- 确保集合名称为: knowledge_base');
  console.log('- 选择JSON格式导入');
  console.log('- 如果文件过大，可以分批次导入');
}

// 初始化
processAllBatches();
showImportGuide();

console.log('\n🎯 数据转换工具已准备就绪！');
console.log('请选择导入方法并开始操作。');
