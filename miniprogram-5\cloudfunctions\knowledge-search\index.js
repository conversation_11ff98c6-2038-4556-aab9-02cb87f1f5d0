// 云函数：knowledge-search
// 用于搜索知识库内容
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 添加详细的日志记录函数
function logInfo(message, data = null) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
}

function logError(message, error = null) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

function logWarning(message, data = null) {
  console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`, data || '');
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { query, category, limit = 20, action = 'search' } = event;

  logInfo('知识库搜索云函数被调用', { action, query, category, limit });
  
  try {
    // 如果是统计请求
    if (action === 'count') {
      logInfo('执行数据库统计查询');

      const countResult = await db.collection('knowledge_base').count();
      logInfo('统计查询完成', { total: countResult.total });

      // 只返回基本信息，不返回完整内容
      const sampleResult = await db.collection('knowledge_base')
        .field({ title: true, category: true, file_size: true, created_at: true })
        .limit(3)
        .get();
      logInfo('示例数据查询完成', { sampleCount: sampleResult.data.length });

      return {
        success: true,
        total: countResult.total,
        sample: sampleResult.data.map(item => ({
          id: item._id,
          title: item.title,
          category: item.category,
          file_size: item.file_size,
          created_at: item.created_at
        })),
        timestamp: new Date().toISOString()
      };
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 分类筛选
    if (category && category !== 'all') {
      whereCondition.category = category;
    }
    
    // 关键词搜索
    if (query && query.trim()) {
      const searchRegex = new RegExp(query.trim(), 'i');
      whereCondition = {
        ...whereCondition,
        $or: [
          { title: searchRegex },
          { author: searchRegex },
          { content: searchRegex },
          { keywords: searchRegex }
        ]
      };
    }
    
    // 执行查询 - 先不包含content字段避免1MB限制
    const result = await db.collection('knowledge_base')
      .where(whereCondition)
      .field({
        title: true,
        file_path: true,
        category: true,
        author: true,
        dynasty: true,
        file_size: true,
        batch_info: true,
        created_at: true,
        keywords: true,
        _id: true
        // 不包含content字段，避免数据过大
      })
      .limit(limit)
      .orderBy('created_at', 'desc')
      .get();
    
    // 如果有搜索关键词，计算相关度并排序
    if (query && query.trim()) {
      const searchTerms = query.toLowerCase().split(/\s+/);

      // 为每个结果获取内容片段并计算相关度
      result.data = await Promise.all(result.data.map(async (item) => {
        let score = 0;
        const title = (item.title || '').toLowerCase();
        const author = (item.author || '').toLowerCase();

        // 计算基础匹配分数
        searchTerms.forEach(term => {
          // 标题匹配权重最高
          if (title.includes(term)) score += 10;
          // 作者匹配权重中等
          if (author.includes(term)) score += 5;
        });

        // 单独获取内容进行匹配和片段提取
        let contentSnippet = '';
        let matchedSnippets = [];

        try {
          const contentResult = await db.collection('knowledge_base')
            .doc(item._id)
            .field({ content: true })
            .get();

          const content = contentResult.data.content || '';
          const contentLower = content.toLowerCase();

          // 计算内容匹配分数
          searchTerms.forEach(term => {
            const contentMatches = (contentLower.match(new RegExp(term, 'g')) || []).length;
            score += contentMatches;
          });

          // 提取匹配的内容片段
          const contentLines = content.split('\n');
          searchTerms.forEach(term => {
            const matchingLines = contentLines.filter(line =>
              line.toLowerCase().includes(term) && line.trim().length > 10
            );
            matchedSnippets.push(...matchingLines.slice(0, 2));
          });

          // 截断内容片段
          contentSnippet = content.length > 500
            ? content.substring(0, 500) + '...[查看更多]'
            : content;

        } catch (err) {
          logWarning('获取内容片段失败', { id: item._id, error: err.message });
          contentSnippet = '[内容获取失败]';
        }

        return {
          ...item,
          content: contentSnippet,
          score,
          relevance: Math.min(score / searchTerms.length, 100),
          matchedSnippets: matchedSnippets.slice(0, 3)
        };
      }));

      // 按相关度排序
      result.data.sort((a, b) => b.score - a.score);
    } else {
      // 如果没有搜索关键词，为每个结果添加简短的内容预览
      result.data = await Promise.all(result.data.map(async (item) => {
        try {
          const contentResult = await db.collection('knowledge_base')
            .doc(item._id)
            .field({ content: true })
            .get();

          const content = contentResult.data.content || '';
          const contentSnippet = content.length > 300
            ? content.substring(0, 300) + '...[查看更多]'
            : content;

          return {
            ...item,
            content: contentSnippet
          };
        } catch (err) {
          logWarning('获取内容预览失败', { id: item._id, error: err.message });
          return {
            ...item,
            content: '[内容获取失败]'
          };
        }
      }));
    }
    
    logInfo('搜索查询完成', { resultCount: result.data.length });

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError('知识库搜索失败', {
      message: error.message,
      stack: error.stack,
      event: event
    });

    return {
      success: false,
      error: error.message,
      errorCode: error.errCode || 'UNKNOWN_ERROR',
      data: [],
      timestamp: new Date().toISOString()
    };
  }
};
