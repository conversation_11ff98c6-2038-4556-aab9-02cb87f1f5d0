// 云函数：knowledge-search
// 用于搜索知识库内容
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

// 添加详细的日志记录函数
function logInfo(message, data = null) {
  console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
}

function logError(message, error = null) {
  console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
}

function logWarning(message, data = null) {
  console.warn(`[WARNING] ${new Date().toISOString()} - ${message}`, data || '');
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  const { query, category, limit = 20, action = 'search' } = event;

  logInfo('知识库搜索云函数被调用', { action, query, category, limit });
  
  try {
    // 如果是统计请求
    if (action === 'count') {
      logInfo('执行数据库统计查询');

      const countResult = await db.collection('knowledge_base').count();
      logInfo('统计查询完成', { total: countResult.total });

      // 只返回基本信息，不返回完整内容
      const sampleResult = await db.collection('knowledge_base')
        .field({ title: true, category: true, file_size: true, created_at: true })
        .limit(3)
        .get();
      logInfo('示例数据查询完成', { sampleCount: sampleResult.data.length });

      return {
        success: true,
        total: countResult.total,
        sample: sampleResult.data.map(item => ({
          id: item._id,
          title: item.title,
          category: item.category,
          file_size: item.file_size,
          created_at: item.created_at
        })),
        timestamp: new Date().toISOString()
      };
    }

    // 构建查询条件
    let whereCondition = {};
    
    // 分类筛选
    if (category && category !== 'all') {
      whereCondition.category = category;
    }
    
    // 关键词搜索
    if (query && query.trim()) {
      const searchRegex = new RegExp(query.trim(), 'i');
      whereCondition = {
        ...whereCondition,
        $or: [
          { title: searchRegex },
          { author: searchRegex },
          { content: searchRegex },
          { keywords: searchRegex }
        ]
      };
    }
    
    // 执行查询
    const result = await db.collection('knowledge_base')
      .where(whereCondition)
      .limit(limit)
      .orderBy('created_at', 'desc')
      .get();
    
    // 如果有搜索关键词，计算相关度并排序
    if (query && query.trim()) {
      const searchTerms = query.toLowerCase().split(/\s+/);
      
      result.data = result.data.map(item => {
        let score = 0;
        const title = (item.title || '').toLowerCase();
        const content = (item.content || '').toLowerCase();
        const author = (item.author || '').toLowerCase();
        
        // 计算匹配分数
        searchTerms.forEach(term => {
          // 标题匹配权重最高
          if (title.includes(term)) score += 10;
          // 作者匹配权重中等
          if (author.includes(term)) score += 5;
          // 内容匹配权重较低
          const contentMatches = (content.match(new RegExp(term, 'g')) || []).length;
          score += contentMatches;
        });
        
        // 提取匹配的内容片段
        const matchedSnippets = [];
        const contentLines = item.content.split('\n');
        
        searchTerms.forEach(term => {
          const matchingLines = contentLines.filter(line => 
            line.toLowerCase().includes(term) && line.trim().length > 10
          );
          matchedSnippets.push(...matchingLines.slice(0, 2));
        });
        
        return {
          ...item,
          score,
          relevance: Math.min(score / searchTerms.length, 100),
          matchedSnippets: matchedSnippets.slice(0, 3)
        };
      });
      
      // 按相关度排序
      result.data.sort((a, b) => b.score - a.score);
    }
    
    logInfo('搜索查询完成', { resultCount: result.data.length });

    return {
      success: true,
      data: result.data,
      total: result.data.length,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    logError('知识库搜索失败', {
      message: error.message,
      stack: error.stack,
      event: event
    });

    return {
      success: false,
      error: error.message,
      errorCode: error.errCode || 'UNKNOWN_ERROR',
      data: [],
      timestamp: new Date().toISOString()
    };
  }
};
