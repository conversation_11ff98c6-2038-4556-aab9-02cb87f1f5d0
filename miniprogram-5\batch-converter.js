// 批次文件转换器 - 自动处理所有44个批次文件
const fs = require('fs');
const path = require('path');

console.log('=== 批次文件转换器 ===');

// 批次文件列表
const batchFiles = [
  'batch-001.json', 'batch-002.json', 'batch-003.json', 'batch-004.json', 'batch-005.json',
  'batch-006.json', 'batch-007.json', 'batch-008.json', 'batch-009.json', 'batch-010.json',
  'batch-011.json', 'batch-012.json', 'batch-013.json', 'batch-014.json', 'batch-015.json',
  'batch-016.json', 'batch-017.json', 'batch-018.json', 'batch-019.json', 'batch-020.json',
  'batch-021.json', 'batch-022.json', 'batch-023.json', 'batch-024a.json', 'batch-024b.json',
  'batch-024c.json', 'batch-025.json', 'batch-026.json', 'batch-027.json', 'batch-028.json',
  'batch-029.json', 'batch-030.json', 'batch-031.json', 'batch-032.json', 'batch-033.json',
  'batch-034.json', 'batch-035.json', 'batch-036.json', 'batch-037.json', 'batch-038.json',
  'batch-039.json', 'batch-040.json', 'batch-041.json', 'batch-042.json', 'batch-043.json',
  'batch-044.json'
];

// 转换单个批次文件
function convertBatchFile(batchFilePath) {
  try {
    console.log(`📖 读取文件: ${batchFilePath}`);
    
    const batchData = JSON.parse(fs.readFileSync(batchFilePath, 'utf8'));
    
    if (!batchData || !batchData.data) {
      console.error(`❌ 文件格式错误: ${batchFilePath}`);
      return [];
    }
    
    const importRecords = [];
    
    batchData.data.forEach((fileData, index) => {
      const record = {
        title: fileData.title || '',
        content: fileData.content || '',
        file_path: fileData.filePath || '',
        category: fileData.category || 'other',
        author: fileData.author || '',
        dynasty: fileData.dynasty || '',
        file_size: fileData.content ? fileData.content.length : 0,
        created_at: new Date().toISOString(),
        batch_info: {
          batch_number: batchData.batchNumber,
          total_batches: batchData.totalBatches,
          original_index: index,
          file_name: batchData.files ? batchData.files[index] : `file_${index}`
        }
      };
      
      importRecords.push(record);
    });
    
    console.log(`✅ 转换完成: ${importRecords.length} 条记录`);
    return importRecords;
    
  } catch (error) {
    console.error(`❌ 转换失败: ${batchFilePath}`, error.message);
    return [];
  }
}

// 处理所有批次文件
function processAllBatches() {
  const uploadBatchesDir = path.join(__dirname, 'upload-batches');
  const allImportData = [];
  let totalFiles = 0;
  let successBatches = 0;
  let failedBatches = 0;
  
  console.log(`\n🚀 开始处理 ${batchFiles.length} 个批次文件...`);
  
  batchFiles.forEach((batchFile, index) => {
    const batchFilePath = path.join(uploadBatchesDir, batchFile);
    
    if (!fs.existsSync(batchFilePath)) {
      console.error(`❌ 文件不存在: ${batchFile}`);
      failedBatches++;
      return;
    }
    
    const batchImportData = convertBatchFile(batchFilePath);
    
    if (batchImportData.length > 0) {
      allImportData.push(...batchImportData);
      totalFiles += batchImportData.length;
      successBatches++;
      
      // 每5个批次显示进度
      if ((index + 1) % 5 === 0) {
        console.log(`📊 进度: ${index + 1}/${batchFiles.length} 批次完成`);
      }
    } else {
      failedBatches++;
    }
  });
  
  console.log(`\n📊 处理完成统计:`);
  console.log(`✅ 成功批次: ${successBatches}`);
  console.log(`❌ 失败批次: ${failedBatches}`);
  console.log(`📁 总文件数: ${totalFiles}`);
  
  if (allImportData.length > 0) {
    // 生成完整的导入文件 - JSON Lines格式
    const outputPath = path.join(__dirname, 'knowledge_base_complete_import.json');
    const jsonLines = allImportData.map(record => JSON.stringify(record)).join('\n');
    fs.writeFileSync(outputPath, jsonLines, 'utf8');
    
    console.log(`\n🎉 导入文件已生成:`);
    console.log(`📁 文件路径: ${outputPath}`);
    console.log(`📊 记录总数: ${allImportData.length}`);
    console.log(`💾 文件大小: ${(fs.statSync(outputPath).size / 1024 / 1024).toFixed(2)} MB`);
    
    // 生成分批导入文件（如果文件太大）
    const fileSizeMB = fs.statSync(outputPath).size / 1024 / 1024;
    if (fileSizeMB > 50) {
      console.log(`\n⚠️ 文件较大 (${fileSizeMB.toFixed(2)} MB)，生成分批导入文件...`);
      generateSplitFiles(allImportData);
    }
    
    // 生成导入指南
    generateImportGuide(outputPath, allImportData.length);
    
  } else {
    console.error(`❌ 没有成功转换任何数据`);
  }
}

// 生成分批导入文件 - JSON Lines格式
function generateSplitFiles(allData) {
  const batchSize = 50; // 每批50条记录
  const totalBatches = Math.ceil(allData.length / batchSize);

  for (let i = 0; i < totalBatches; i++) {
    const start = i * batchSize;
    const end = Math.min(start + batchSize, allData.length);
    const batchData = allData.slice(start, end);

    const fileName = `knowledge_base_import_part_${(i + 1).toString().padStart(3, '0')}.json`;
    const filePath = path.join(__dirname, fileName);

    // 生成JSON Lines格式
    const jsonLines = batchData.map(record => JSON.stringify(record)).join('\n');
    fs.writeFileSync(filePath, jsonLines, 'utf8');
    console.log(`📁 生成分批文件: ${fileName} (${batchData.length} 条记录)`);
  }

  console.log(`✅ 已生成 ${totalBatches} 个分批导入文件`);
}

// 生成导入指南
function generateImportGuide(filePath, totalRecords) {
  const guide = `
# 知识库数据导入指南

## 文件信息
- 📁 导入文件: ${path.basename(filePath)}
- 📊 记录总数: ${totalRecords} 条
- 🕒 生成时间: ${new Date().toLocaleString()}

## 导入步骤

### 方法1: 完整导入（推荐）
1. 打开微信开发者工具
2. 进入云开发控制台
3. 选择数据库 → knowledge_base 集合
4. 点击"导入"按钮
5. 选择文件: ${path.basename(filePath)}
6. 确认导入

### 方法2: 分批导入（如果完整导入失败）
如果生成了分批文件，请按顺序导入：
1. knowledge_base_import_part_001.json
2. knowledge_base_import_part_002.json
3. ... 依此类推

## 导入后验证
导入完成后，请运行以下代码验证：

\`\`\`javascript
// 检查记录总数
wx.cloud.database().collection('knowledge_base').count().then(res => {
  console.log('数据库记录总数:', res.total);
  console.log('预期记录数:', ${totalRecords});
});

// 检查批次分布
wx.cloud.database().collection('knowledge_base')
  .field({ batch_info: true, title: true })
  .limit(20)
  .get()
  .then(res => {
    console.log('样本数据:', res.data);
  });
\`\`\`

## 注意事项
- 确保集合名称为: knowledge_base
- 导入格式选择: JSON
- 如遇到错误，请检查文件格式和大小限制
- 建议在导入前备份现有数据

## 数据结构说明
每条记录包含以下字段：
- title: 文档标题
- content: 文档内容
- file_path: 原始文件路径
- category: 分类
- author: 作者
- dynasty: 朝代
- file_size: 文件大小
- created_at: 创建时间
- batch_info: 批次信息
  - batch_number: 批次号
  - total_batches: 总批次数
  - original_index: 原始索引
  - file_name: 原始文件名
`;

  const guidePath = path.join(__dirname, 'IMPORT_GUIDE.md');
  fs.writeFileSync(guidePath, guide, 'utf8');
  console.log(`📋 导入指南已生成: ${guidePath}`);
}

// 执行转换
if (require.main === module) {
  processAllBatches();
}

module.exports = {
  convertBatchFile,
  processAllBatches
};
