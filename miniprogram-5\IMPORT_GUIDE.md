
# 知识库数据导入指南

## 文件信息
- 📁 导入文件: knowledge_base_complete_import.json
- 📊 记录总数: 437 条
- 🕒 生成时间: 2025/6/29 11:57:05

## 导入步骤

### 方法1: 完整导入（推荐）
1. 打开微信开发者工具
2. 进入云开发控制台
3. 选择数据库 → knowledge_base 集合
4. 点击"导入"按钮
5. 选择文件: knowledge_base_complete_import.json
6. 确认导入

### 方法2: 分批导入（如果完整导入失败）
如果生成了分批文件，请按顺序导入：
1. knowledge_base_import_part_001.json
2. knowledge_base_import_part_002.json
3. ... 依此类推

## 导入后验证
导入完成后，请运行以下代码验证：

```javascript
// 检查记录总数
wx.cloud.database().collection('knowledge_base').count().then(res => {
  console.log('数据库记录总数:', res.total);
  console.log('预期记录数:', 437);
});

// 检查批次分布
wx.cloud.database().collection('knowledge_base')
  .field({ batch_info: true, title: true })
  .limit(20)
  .get()
  .then(res => {
    console.log('样本数据:', res.data);
  });
```

## 注意事项
- 确保集合名称为: knowledge_base
- 导入格式选择: JSON
- 如遇到错误，请检查文件格式和大小限制
- 建议在导入前备份现有数据

## 数据结构说明
每条记录包含以下字段：
- title: 文档标题
- content: 文档内容
- file_path: 原始文件路径
- category: 分类
- author: 作者
- dynasty: 朝代
- file_size: 文件大小
- created_at: 创建时间
- batch_info: 批次信息
  - batch_number: 批次号
  - total_batches: 总批次数
  - original_index: 原始索引
  - file_name: 原始文件名
