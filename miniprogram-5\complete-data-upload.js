// 完整数据重新上传脚本
// 使用upload-batches目录下的44个JSON文件

console.log('=== 完整知识库数据重新上传 ===');

// 批次文件列表（44个批次）
const batchFiles = [
  'batch-001.json', 'batch-002.json', 'batch-003.json', 'batch-004.json', 'batch-005.json',
  'batch-006.json', 'batch-007.json', 'batch-008.json', 'batch-009.json', 'batch-010.json',
  'batch-011.json', 'batch-012.json', 'batch-013.json', 'batch-014.json', 'batch-015.json',
  'batch-016.json', 'batch-017.json', 'batch-018.json', 'batch-019.json', 'batch-020.json',
  'batch-021.json', 'batch-022.json', 'batch-023.json', 'batch-024a.json', 'batch-024b.json',
  'batch-024c.json', 'batch-025.json', 'batch-026.json', 'batch-027.json', 'batch-028.json',
  'batch-029.json', 'batch-030.json', 'batch-031.json', 'batch-032.json', 'batch-033.json',
  'batch-034.json', 'batch-035.json', 'batch-036.json', 'batch-037.json', 'batch-038.json',
  'batch-039.json', 'batch-040.json', 'batch-041.json', 'batch-042.json', 'batch-043.json',
  'batch-044.json'
];

// 上传状态跟踪
let uploadStatus = {
  total: batchFiles.length,
  completed: 0,
  failed: 0,
  errors: [],
  startTime: new Date(),
  currentBatch: null
};

// 上传单个批次
async function uploadBatch(batchFile, batchIndex) {
  console.log(`\n📦 开始上传批次 ${batchIndex + 1}/${batchFiles.length}: ${batchFile}`);
  uploadStatus.currentBatch = batchFile;
  
  try {
    // 这里需要您手动复制对应的批次数据
    console.log(`⚠️ 请手动复制 upload-batches/${batchFile} 的内容`);
    console.log('然后运行: uploadBatchData(batchData)');
    
    return new Promise((resolve) => {
      // 创建全局函数供手动调用
      window[`uploadBatch${batchIndex + 1}`] = async function(batchData) {
        try {
          console.log(`📤 正在上传批次 ${batchIndex + 1} 数据...`);
          
          if (!batchData || !batchData.data) {
            throw new Error('批次数据格式错误');
          }
          
          const results = [];
          let successCount = 0;
          let errorCount = 0;
          
          // 逐个上传文件
          for (let i = 0; i < batchData.data.length; i++) {
            const fileData = batchData.data[i];
            
            try {
              const record = {
                title: fileData.title,
                content: fileData.content,
                file_path: fileData.filePath,
                category: fileData.category || 'other',
                author: fileData.author || '',
                dynasty: fileData.dynasty || '',
                file_size: fileData.content ? fileData.content.length : 0,
                created_at: new Date(),
                batch_info: {
                  batch_number: batchData.batchNumber,
                  total_batches: batchData.totalBatches,
                  original_index: i,
                  file_name: batchData.files[i]
                }
              };
              
              const result = await wx.cloud.database().collection('knowledge_base').add({
                data: record
              });
              
              results.push({ success: true, id: result._id, file: fileData.filePath });
              successCount++;
              
              if (successCount % 2 === 0) {
                console.log(`  ✅ 已上传 ${successCount}/${batchData.data.length} 个文件`);
              }
              
            } catch (error) {
              console.error(`  ❌ 上传失败: ${fileData.filePath}`, error);
              results.push({ success: false, error: error.message, file: fileData.filePath });
              errorCount++;
            }
            
            // 短暂延迟避免请求过快
            await new Promise(resolve => setTimeout(resolve, 100));
          }
          
          console.log(`\n📊 批次 ${batchIndex + 1} 上传完成:`);
          console.log(`  ✅ 成功: ${successCount} 个文件`);
          console.log(`  ❌ 失败: ${errorCount} 个文件`);
          
          uploadStatus.completed++;
          if (errorCount > 0) {
            uploadStatus.failed++;
            uploadStatus.errors.push({
              batch: batchFile,
              errorCount: errorCount,
              errors: results.filter(r => !r.success)
            });
          }
          
          // 显示总体进度
          console.log(`\n🎯 总体进度: ${uploadStatus.completed}/${uploadStatus.total} 批次完成`);
          
          resolve({ success: true, successCount, errorCount, results });
          
        } catch (error) {
          console.error(`❌ 批次 ${batchIndex + 1} 上传失败:`, error);
          uploadStatus.failed++;
          uploadStatus.errors.push({
            batch: batchFile,
            error: error.message
          });
          resolve({ success: false, error: error.message });
        }
      };
      
      console.log(`💡 请运行: uploadBatch${batchIndex + 1}(batchData)`);
    });
  } catch (error) {
    console.error(`❌ 批次 ${batchIndex + 1} 准备失败:`, error);
    return { success: false, error: error.message };
  }
}

// 验证上传结果
async function verifyUpload() {
  console.log('\n🔍 验证上传结果...');
  
  try {
    const db = wx.cloud.database();
    const countResult = await db.collection('knowledge_base').count();
    
    console.log(`📊 数据库中总记录数: ${countResult.total}`);
    
    // 检查批次分布
    const batchStats = {};
    const sampleData = await db.collection('knowledge_base')
      .field({ batch_info: true, title: true })
      .limit(100)
      .get();
    
    sampleData.data.forEach(record => {
      if (record.batch_info && record.batch_info.batch_number) {
        const batchNum = record.batch_info.batch_number;
        batchStats[batchNum] = (batchStats[batchNum] || 0) + 1;
      }
    });
    
    console.log('📦 批次分布（前100条记录）:', batchStats);
    
    return {
      total: countResult.total,
      batchStats: batchStats,
      expectedTotal: 437 // 预期的文件总数
    };
    
  } catch (error) {
    console.error('❌ 验证失败:', error);
    return null;
  }
}

// 显示上传指南
function showUploadGuide() {
  console.log('\n📋 上传指南:');
  console.log('1. 运行 startUpload() 开始上传流程');
  console.log('2. 按提示手动复制每个批次的JSON数据');
  console.log('3. 调用对应的 uploadBatchX(batchData) 函数');
  console.log('4. 完成所有批次后运行 verifyUpload() 验证结果');
  console.log('5. 运行 showUploadStatus() 查看上传状态');
}

// 开始上传流程
async function startUpload() {
  console.log('\n🚀 开始完整数据上传流程...');
  console.log(`📦 总共需要上传 ${batchFiles.length} 个批次`);
  
  uploadStatus.startTime = new Date();
  
  // 逐个处理批次
  for (let i = 0; i < batchFiles.length; i++) {
    await uploadBatch(batchFiles[i], i);
    
    // 每5个批次暂停一下
    if ((i + 1) % 5 === 0) {
      console.log(`\n⏸️ 已处理 ${i + 1} 个批次，请继续...`);
    }
  }
  
  console.log('\n✅ 所有批次准备完成！');
  console.log('请按照提示逐个上传批次数据');
}

// 显示上传状态
function showUploadStatus() {
  const elapsed = new Date() - uploadStatus.startTime;
  const elapsedMinutes = Math.floor(elapsed / 60000);
  
  console.log('\n📊 上传状态总结:');
  console.log(`⏱️ 已用时: ${elapsedMinutes} 分钟`);
  console.log(`✅ 已完成: ${uploadStatus.completed}/${uploadStatus.total} 批次`);
  console.log(`❌ 失败: ${uploadStatus.failed} 批次`);
  console.log(`🔄 当前批次: ${uploadStatus.currentBatch || '无'}`);
  
  if (uploadStatus.errors.length > 0) {
    console.log('\n❌ 错误详情:');
    uploadStatus.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.batch}: ${error.error || '部分文件上传失败'}`);
    });
  }
  
  if (uploadStatus.completed === uploadStatus.total) {
    console.log('\n🎉 所有批次上传完成！');
    console.log('请运行 verifyUpload() 验证最终结果');
  }
}

// 导出函数
window.startUpload = startUpload;
window.verifyUpload = verifyUpload;
window.showUploadStatus = showUploadStatus;
window.showUploadGuide = showUploadGuide;

// 显示初始指南
showUploadGuide();
console.log('\n🎯 脚本已加载完成！请运行 startUpload() 开始上传');
