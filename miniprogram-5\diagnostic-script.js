// 微信云开发环境诊断脚本
const fs = require('fs');
const path = require('path');

console.log('=== 微信云开发环境诊断 ===');
console.log('项目路径:', process.cwd());

// 1. 检查项目配置文件
console.log('\n1. 检查项目配置文件:');
try {
  const projectConfig = JSON.parse(fs.readFileSync('project.config.json', 'utf8'));
  console.log('- APPID:', projectConfig.appid);
  console.log('- 项目名称:', projectConfig.projectname);
  console.log('- 云函数根目录:', projectConfig.cloudfunctionRoot);
  console.log('- 小程序根目录:', projectConfig.miniprogramRoot);
} catch (e) {
  console.log('- 项目配置文件读取失败:', e.message);
}

// 2. 检查云函数目录
console.log('\n2. 检查云函数目录:');
try {
  const cloudFunctions = fs.readdirSync('cloudfunctions');
  console.log('- 云函数列表:', cloudFunctions);
  
  cloudFunctions.forEach(func => {
    const funcPath = path.join('cloudfunctions', func);
    if (fs.statSync(funcPath).isDirectory()) {
      const indexPath = path.join(funcPath, 'index.js');
      const packagePath = path.join(funcPath, 'package.json');
      if (fs.existsSync(indexPath)) {
        console.log(`  ✓ ${func} - index.js存在`);
      } else {
        console.log(`  ✗ ${func} - index.js缺失`);
      }
      if (fs.existsSync(packagePath)) {
        console.log(`    - package.json存在`);
      } else {
        console.log(`    - package.json缺失`);
      }
    }
  });
} catch (e) {
  console.log('- 云函数目录读取失败:', e.message);
}

// 3. 检查知识库批次文件
console.log('\n3. 检查知识库批次文件:');
try {
  const batchFiles = fs.readdirSync('upload-batches').filter(f => f.startsWith('batch-') && f.endsWith('.json'));
  console.log('- 批次文件数量:', batchFiles.length);
  console.log('- 批次文件范围:', batchFiles.slice(0, 3).join(', '), '...', batchFiles.slice(-3).join(', '));
  
  // 检查几个关键批次文件的大小和内容
  [1, 24, 25].forEach(num => {
    const fileName = `batch-${num.toString().padStart(3, '0')}.json`;
    const filePath = path.join('upload-batches', fileName);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`  - ${fileName}: ${Math.round(stats.size/1024)}KB`);
      
      // 检查JSON格式是否正确
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        console.log(`    ✓ JSON格式正确，包含${content.length}个文件`);
      } catch (jsonError) {
        console.log(`    ✗ JSON格式错误:`, jsonError.message);
      }
    } else {
      console.log(`  - ${fileName}: 文件不存在`);
    }
  });
} catch (e) {
  console.log('- 批次文件检查失败:', e.message);
}

// 4. 检查环境配置
console.log('\n4. 检查环境配置:');
try {
  const envList = require('./miniprogram/envList.js');
  console.log('- envList配置:', JSON.stringify(envList, null, 2));
  
  if (!envList.envList || envList.envList.length === 0) {
    console.log('  ⚠️ envList为空，这可能是数据库连接问题的原因');
  }
} catch (e) {
  console.log('- envList读取失败:', e.message);
}

// 5. 检查app.js中的云开发配置
console.log('\n5. 检查app.js云开发配置:');
try {
  const appJs = fs.readFileSync('miniprogram/app.js', 'utf8');
  
  // 提取环境ID
  const envMatch = appJs.match(/env:\s*["']([^"']+)["']/);
  if (envMatch) {
    console.log('- 云开发环境ID:', envMatch[1]);
  } else {
    console.log('- 未找到明确的环境ID配置');
  }
  
  // 检查初始化方式
  if (appJs.includes('wx.cloud.init')) {
    console.log('- ✓ 包含云开发初始化代码');
  } else {
    console.log('- ✗ 缺少云开发初始化代码');
  }
} catch (e) {
  console.log('- app.js检查失败:', e.message);
}

// 6. 检查上传脚本
console.log('\n6. 检查上传相关脚本:');
const uploadScripts = [
  'batch-upload-script.js',
  'upload-knowledge.js',
  'auto-upload-script.js'
];

uploadScripts.forEach(script => {
  if (fs.existsSync(script)) {
    const stats = fs.statSync(script);
    console.log(`- ✓ ${script}: ${Math.round(stats.size/1024)}KB`);
  } else {
    console.log(`- ✗ ${script}: 文件不存在`);
  }
});

console.log('\n=== 诊断完成 ===');
console.log('\n建议检查项目:');
console.log('1. 确认云开发环境ID是否正确');
console.log('2. 检查envList.js配置是否完整');
console.log('3. 验证云函数是否正确部署');
console.log('4. 确认数据库权限设置');
