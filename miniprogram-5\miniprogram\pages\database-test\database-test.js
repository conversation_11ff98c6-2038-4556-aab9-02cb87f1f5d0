// 数据库连接测试页面
Page({
  data: {
    testResults: [],
    isLoading: false,
    totalRecords: 0,
    cloudInitStatus: '未测试',
    databaseStatus: '未测试',
    cloudFunctionStatus: '未测试',
    permissionStatus: '未测试'
  },

  onLoad() {
    console.log('数据库测试页面加载');
    this.addLog('页面初始化完成');
  },

  // 添加日志
  addLog(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const log = {
      time: timestamp,
      message: message,
      type: type // info, success, error, warning
    };
    
    this.setData({
      testResults: [...this.data.testResults, log]
    });
    
    console.log(`[${timestamp}] ${message}`);
  },

  // 清空日志
  clearLogs() {
    this.setData({
      testResults: [],
      totalRecords: 0,
      cloudInitStatus: '未测试',
      databaseStatus: '未测试', 
      cloudFunctionStatus: '未测试',
      permissionStatus: '未测试'
    });
  },

  // 开始完整测试
  async startFullTest() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.clearLogs();
    this.addLog('开始执行完整数据库测试...', 'info');
    
    try {
      // 1. 测试云开发初始化
      await this.testCloudInit();
      
      // 2. 测试数据库连接
      await this.testDatabaseConnection();
      
      // 3. 测试云函数调用
      await this.testCloudFunction();
      
      // 4. 测试数据库权限
      await this.testDatabasePermissions();
      
      this.addLog('=== 完整测试完成 ===', 'success');
      this.showTestSummary();
      
    } catch (error) {
      this.addLog(`测试过程中发生错误: ${error.message}`, 'error');
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 测试云开发初始化
  async testCloudInit() {
    this.addLog('1. 测试云开发初始化状态...', 'info');
    
    try {
      if (!wx.cloud) {
        throw new Error('wx.cloud未定义，云开发未初始化');
      }
      
      this.addLog('✅ wx.cloud已定义', 'success');
      this.setData({ cloudInitStatus: '正常' });
      return true;
    } catch (error) {
      this.addLog(`❌ 云开发初始化失败: ${error.message}`, 'error');
      this.setData({ cloudInitStatus: '失败' });
      throw error;
    }
  },

  // 测试数据库连接
  async testDatabaseConnection() {
    this.addLog('2. 测试数据库连接...', 'info');
    
    try {
      const db = wx.cloud.database();
      this.addLog('✅ 数据库对象创建成功', 'success');
      
      const result = await db.collection('knowledge_base').count();
      this.addLog('✅ 数据库查询成功', 'success');
      this.addLog(`📊 知识库记录总数: ${result.total}`, 'info');
      
      this.setData({ 
        databaseStatus: '正常',
        totalRecords: result.total
      });
      
      if (result.total === 0) {
        this.addLog('⚠️ 警告: 知识库中没有数据！', 'warning');
      }
      
      return result.total;
    } catch (error) {
      this.addLog(`❌ 数据库连接失败: ${error.errMsg || error.message}`, 'error');
      this.addLog(`错误代码: ${error.errCode}`, 'error');
      this.setData({ databaseStatus: '失败' });
      throw error;
    }
  },

  // 测试云函数调用
  async testCloudFunction() {
    this.addLog('3. 测试云函数调用...', 'info');
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'knowledge-search',
        data: {
          action: 'count'
        }
      });
      
      this.addLog('✅ 云函数调用成功', 'success');
      this.addLog(`📊 云函数返回结果: ${JSON.stringify(result.result)}`, 'info');
      
      this.setData({ cloudFunctionStatus: '正常' });
      return result.result;
    } catch (error) {
      this.addLog(`❌ 云函数调用失败: ${error.errMsg || error.message}`, 'error');
      this.addLog(`错误代码: ${error.errCode}`, 'error');
      this.setData({ cloudFunctionStatus: '失败' });
      // 云函数失败不阻断后续测试
      return null;
    }
  },

  // 测试数据库权限
  async testDatabasePermissions() {
    this.addLog('4. 测试数据库权限...', 'info');
    
    try {
      const db = wx.cloud.database();
      
      // 测试读权限
      const readResult = await db.collection('knowledge_base').limit(1).get();
      this.addLog('✅ 数据库读权限正常', 'success');
      this.addLog(`📖 示例数据: ${readResult.data.length > 0 ? '有数据' : '无数据'}`, 'info');
      
      // 测试写权限（添加测试记录）
      const testDoc = {
        title: '数据库连接测试',
        content: '这是一个测试记录',
        created_at: new Date(),
        test_record: true
      };
      
      const writeResult = await db.collection('knowledge_base').add({
        data: testDoc
      });
      this.addLog('✅ 数据库写权限正常', 'success');
      this.addLog(`📝 测试记录ID: ${writeResult._id}`, 'info');
      
      // 删除测试记录
      await db.collection('knowledge_base').doc(writeResult._id).remove();
      this.addLog('✅ 数据库删除权限正常', 'success');
      
      this.setData({ permissionStatus: '正常' });
      return true;
    } catch (error) {
      this.addLog(`❌ 数据库权限测试失败: ${error.errMsg || error.message}`, 'error');
      this.addLog(`错误代码: ${error.errCode}`, 'error');
      this.setData({ permissionStatus: '失败' });
      return false;
    }
  },

  // 显示测试总结
  showTestSummary() {
    const { cloudInitStatus, databaseStatus, cloudFunctionStatus, permissionStatus, totalRecords } = this.data;
    
    this.addLog('=== 测试结果总结 ===', 'info');
    this.addLog(`云开发初始化: ${cloudInitStatus}`, cloudInitStatus === '正常' ? 'success' : 'error');
    this.addLog(`数据库连接: ${databaseStatus}`, databaseStatus === '正常' ? 'success' : 'error');
    this.addLog(`云函数调用: ${cloudFunctionStatus}`, cloudFunctionStatus === '正常' ? 'success' : 'warning');
    this.addLog(`数据库权限: ${permissionStatus}`, permissionStatus === '正常' ? 'success' : 'error');
    this.addLog(`知识库记录数: ${totalRecords}`, totalRecords > 0 ? 'success' : 'warning');
    
    if (totalRecords === 0) {
      this.addLog('⚠️ 建议检查数据上传状态', 'warning');
    }
  },

  // 单独测试数据库连接
  async testDatabaseOnly() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.addLog('开始测试数据库连接...', 'info');
    
    try {
      await this.testCloudInit();
      await this.testDatabaseConnection();
    } catch (error) {
      this.addLog(`数据库测试失败: ${error.message}`, 'error');
    } finally {
      this.setData({ isLoading: false });
    }
  },

  // 单独测试云函数
  async testCloudFunctionOnly() {
    if (this.data.isLoading) return;
    
    this.setData({ isLoading: true });
    this.addLog('开始测试云函数调用...', 'info');
    
    try {
      await this.testCloudInit();
      await this.testCloudFunction();
    } catch (error) {
      this.addLog(`云函数测试失败: ${error.message}`, 'error');
    } finally {
      this.setData({ isLoading: false });
    }
  }
});
