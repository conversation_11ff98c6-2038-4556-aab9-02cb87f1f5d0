# 知识库重新导入指南

## 🧹 第一步：清空数据库

### 方法1：使用脚本清空（推荐）
```bash
cd miniprogram-5
node clear-database.js
```

### 方法2：使用云函数直接调用
在微信开发者工具的云函数控制台中调用 `data-cleaner`：
```json
{
  "action": "clear_all",
  "confirm": true
}
```

### 方法3：使用云开发控制台
1. 打开微信云开发控制台
2. 进入数据库管理
3. 选择 `knowledge_base` 集合
4. 删除所有记录

## 📥 第二步：重新导入数据

### 准备工作
确认以下文件已生成且包含批次24：
- `knowledge_base_complete_import.json` (81.69 MB, 437条记录)
- `knowledge_base_import_part_001.json` 到 `knowledge_base_import_part_009.json`

### 导入方法

#### 方法1：使用云开发控制台导入（推荐）

1. **打开云开发控制台**
   - 访问：https://console.cloud.tencent.com/tcb
   - 选择环境：`cloud1-0g3xctv612d8f755`

2. **进入数据库管理**
   - 点击左侧菜单"数据库"
   - 选择 `knowledge_base` 集合

3. **导入数据**
   - 点击"导入"按钮
   - 选择"JSON Lines"格式
   - 上传分批文件（建议从 part_001 开始）

4. **分批导入顺序**
   ```
   knowledge_base_import_part_001.json  (约48条记录)
   knowledge_base_import_part_002.json  (约48条记录)
   knowledge_base_import_part_003.json  (约48条记录)
   knowledge_base_import_part_004.json  (约48条记录)
   knowledge_base_import_part_005.json  (约49条记录)
   knowledge_base_import_part_006.json  (约49条记录)
   knowledge_base_import_part_007.json  (约49条记录)
   knowledge_base_import_part_008.json  (约49条记录)
   knowledge_base_import_part_009.json  (约49条记录)
   ```

#### 方法2：使用云函数批量上传
```javascript
// 在云函数中调用
const result = await cloud.callFunction({
  name: 'batchUploadKnowledge',
  data: {
    // 数据内容
  }
});
```

## 🔍 第三步：验证导入结果

### 检查记录总数
```bash
node -e "
const cloud = require('wx-server-sdk');
cloud.init({env: 'cloud1-0g3xctv612d8f755'});
cloud.callFunction({
  name: 'data-cleaner',
  data: { action: 'count' }
}).then(result => {
  console.log('总记录数:', result.result.total);
  console.log('预期记录数: 437');
  console.log('导入状态:', result.result.total === 437 ? '✅ 完整' : '⚠️ 不完整');
});
"
```

### 检查批次24是否存在
```bash
node -e "
const cloud = require('wx-server-sdk');
cloud.init({env: 'cloud1-0g3xctv612d8f755'});
cloud.callFunction({
  name: 'knowledge-search',
  data: {
    query: '紫微斗数',
    limit: 5
  }
}).then(result => {
  console.log('批次24相关记录:', result.result.results.length);
  result.result.results.forEach(item => {
    console.log('- 标题:', item.title);
    console.log('  批次:', item.batch_info?.batch_number);
  });
});
"
```

### 验证各批次分布
```bash
node -e "
const cloud = require('wx-server-sdk');
cloud.init({env: 'cloud1-0g3xctv612d8f755'});
cloud.callFunction({
  name: 'data-validator',
  data: {
    action: 'check_batch_distribution'
  }
}).then(result => {
  console.log('批次分布验证结果:');
  console.log(JSON.stringify(result.result, null, 2));
});
"
```

## 📊 预期结果

导入完成后应该有：
- **总记录数**: 437条
- **批次范围**: 1-44（除了原本缺失的批次）
- **批次24**: 包含10条紫微斗数相关记录
- **文件类型**: 涵盖易经、八字、紫微斗数等各类古籍

## ⚠️ 注意事项

1. **文件大小限制**: 单个文件不要超过10MB
2. **格式要求**: 必须是JSON Lines格式（每行一个JSON对象）
3. **网络稳定**: 确保网络连接稳定，避免上传中断
4. **分批导入**: 建议分批导入，避免一次性上传过多数据
5. **验证完整性**: 每批导入后都要验证记录数

## 🆘 故障排除

### 如果导入失败
1. 检查文件格式是否正确
2. 确认云开发环境ID是否正确
3. 检查网络连接
4. 查看云函数日志

### 如果记录数不对
1. 重新清空数据库
2. 检查所有分批文件是否完整
3. 逐个文件导入并验证

### 如果批次24仍然缺失
1. 确认 `batch-converter.js` 已修复
2. 重新运行转换器生成新文件
3. 检查 `batch-024.json` 是否存在且有效

## 🎉 完成确认

导入完成后，您应该能够：
- 搜索到紫微斗数相关内容
- 查询到437条记录
- 各个批次的数据都能正常检索
- AI功能可以正常调用知识库内容
