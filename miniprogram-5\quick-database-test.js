// 快速数据库测试脚本 - 在微信开发者工具控制台中运行

console.log('=== 快速数据库连接测试 ===');

// 测试1：基本连接测试
async function testBasicConnection() {
  console.log('\n1. 测试基本数据库连接...');
  try {
    const db = wx.cloud.database();
    const result = await db.collection('knowledge_base').count();
    console.log('✅ 数据库连接成功');
    console.log(`📊 知识库记录总数: ${result.total}`);
    return result.total;
  } catch (error) {
    console.log('❌ 数据库连接失败:', error.errMsg || error.message);
    return -1;
  }
}

// 测试2：云函数测试
async function testCloudFunction() {
  console.log('\n2. 测试云函数调用...');
  try {
    const result = await wx.cloud.callFunction({
      name: 'knowledge-search',
      data: { action: 'count' }
    });
    console.log('✅ 云函数调用成功');
    console.log('📊 云函数返回结果:', result.result);
    return result.result;
  } catch (error) {
    console.log('❌ 云函数调用失败:', error.errMsg || error.message);
    return null;
  }
}

// 测试3：简化的数据验证
async function testDataValidation() {
  console.log('\n3. 测试数据验证...');
  try {
    const result = await wx.cloud.callFunction({
      name: 'data-validator',
      data: { action: 'health_check' }
    });
    console.log('✅ 数据验证成功');
    console.log('📊 健康检查结果:', result.result.health);
    return result.result;
  } catch (error) {
    console.log('❌ 数据验证失败:', error.errMsg || error.message);
    return null;
  }
}

// 主测试函数
async function runQuickTest() {
  console.log('开始执行快速数据库测试...\n');
  
  const basicResult = await testBasicConnection();
  const cloudResult = await testCloudFunction();
  const validationResult = await testDataValidation();
  
  console.log('\n=== 测试结果总结 ===');
  console.log(`数据库连接: ${basicResult >= 0 ? '✅ 正常' : '❌ 失败'}`);
  console.log(`云函数调用: ${cloudResult ? '✅ 正常' : '❌ 失败'}`);
  console.log(`数据验证: ${validationResult ? '✅ 正常' : '❌ 失败'}`);
  console.log(`知识库记录数: ${basicResult}`);
  
  if (basicResult > 0) {
    console.log('\n🎉 好消息！数据库中有数据，数据并没有丢失！');
    console.log('问题可能是：');
    console.log('1. 之前的查询方法有问题');
    console.log('2. 环境配置问题已经通过修复envList.js解决');
    console.log('3. 需要重新部署云函数');
  } else if (basicResult === 0) {
    console.log('\n⚠️ 数据库连接正常，但没有数据');
    console.log('需要重新上传知识库数据');
  } else {
    console.log('\n❌ 数据库连接仍有问题');
    console.log('需要进一步检查环境配置');
  }
  
  console.log('\n=== 测试完成 ===');
}

// 导出函数供控制台调用
window.runQuickTest = runQuickTest;
window.testBasicConnection = testBasicConnection;
window.testCloudFunction = testCloudFunction;
window.testDataValidation = testDataValidation;

console.log('快速测试脚本已加载！');
console.log('请在控制台中运行: runQuickTest()');
