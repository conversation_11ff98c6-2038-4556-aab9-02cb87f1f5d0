// 清空数据库脚本
// 使用方法：node clear-database.js

const cloud = require('wx-server-sdk');

// 初始化云开发
cloud.init({
  env: 'cloud1-0g3xctv612d8f755' // 您的云开发环境ID
});

async function clearDatabase() {
  console.log('🧹 开始清空数据库...');
  
  try {
    // 调用data-cleaner云函数
    const result = await cloud.callFunction({
      name: 'data-cleaner',
      data: {
        action: 'clear_all',
        confirm: true  // 确认清空操作
      }
    });
    
    console.log('✅ 清空操作完成！');
    console.log('📊 结果详情：');
    console.log(`   - 删除记录数：${result.result.results.deleted_count}`);
    console.log(`   - 错误数量：${result.result.results.error_count}`);
    console.log(`   - 剩余记录数：${result.result.results.remaining_count}`);
    console.log(`   - 批次数量：${result.result.results.batch_count}`);
    console.log(`   - 状态：${result.result.message}`);
    
    if (result.result.results.error_count > 0) {
      console.log('⚠️  删除过程中的错误：');
      result.result.results.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ID: ${error.id}, 错误: ${error.error}`);
      });
    }
    
    return result.result.results.remaining_count === 0;
    
  } catch (error) {
    console.error('❌ 清空数据库失败：', error);
    return false;
  }
}

async function checkDatabaseStatus() {
  console.log('📊 检查数据库状态...');
  
  try {
    const result = await cloud.callFunction({
      name: 'data-cleaner',
      data: {
        action: 'count'
      }
    });
    
    console.log(`📈 当前数据库记录数：${result.result.total}`);
    return result.result.total;
    
  } catch (error) {
    console.error('❌ 检查数据库状态失败：', error);
    return -1;
  }
}

async function main() {
  console.log('🚀 数据库清空工具启动');
  console.log('=' * 50);
  
  // 1. 检查当前状态
  const initialCount = await checkDatabaseStatus();
  if (initialCount === -1) {
    console.log('❌ 无法连接到数据库，请检查云开发配置');
    return;
  }
  
  if (initialCount === 0) {
    console.log('✅ 数据库已经是空的，无需清空');
    return;
  }
  
  console.log(`\n⚠️  即将清空数据库中的 ${initialCount} 条记录`);
  console.log('⏳ 开始执行清空操作...\n');
  
  // 2. 执行清空
  const success = await clearDatabase();
  
  // 3. 验证结果
  console.log('\n🔍 验证清空结果...');
  const finalCount = await checkDatabaseStatus();
  
  if (success && finalCount === 0) {
    console.log('\n🎉 数据库清空成功！现在可以重新导入数据了。');
  } else {
    console.log(`\n⚠️  清空可能不完整，还剩余 ${finalCount} 条记录`);
    console.log('💡 建议：可以再次运行此脚本或手动检查');
  }
  
  console.log('\n' + '=' * 50);
  console.log('🏁 清空操作完成');
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  clearDatabase,
  checkDatabaseStatus
};
