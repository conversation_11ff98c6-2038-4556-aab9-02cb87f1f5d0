/* 数据库连接测试页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 状态概览 */
.status-overview {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.status-value.success {
  color: #52c41a;
  background-color: #f6ffed;
}

.status-value.error {
  color: #ff4d4f;
  background-color: #fff2f0;
}

.status-value.warning {
  color: #faad14;
  background-color: #fffbe6;
}

.status-value.pending {
  color: #666;
  background-color: #f5f5f5;
}

/* 按钮组 */
.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.test-button {
  flex: 1;
  min-width: 200rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
}

.test-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.test-button.secondary {
  background: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.test-button.clear {
  background: #f5f5f5;
  color: #666;
}

.test-button[disabled] {
  opacity: 0.6;
}

/* 日志容器 */
.log-container {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.log-count {
  font-size: 24rpx;
  color: #666;
}

.log-content {
  height: 600rpx;
  padding: 20rpx;
}

.log-item {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  line-height: 1.4;
}

.log-item.info {
  background-color: #f0f9ff;
  border-left: 4rpx solid #1890ff;
}

.log-item.success {
  background-color: #f6ffed;
  border-left: 4rpx solid #52c41a;
}

.log-item.error {
  background-color: #fff2f0;
  border-left: 4rpx solid #ff4d4f;
}

.log-item.warning {
  background-color: #fffbe6;
  border-left: 4rpx solid #faad14;
}

.log-time {
  color: #666;
  margin-right: 20rpx;
  min-width: 120rpx;
  font-size: 24rpx;
}

.log-message {
  flex: 1;
  word-break: break-all;
}

.log-empty {
  text-align: center;
  color: #999;
  padding: 100rpx 0;
  font-size: 28rpx;
}

/* 加载覆盖层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  padding: 60rpx;
  border-radius: 20rpx;
  text-align: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #333;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
