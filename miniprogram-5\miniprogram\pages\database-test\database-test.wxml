<!--数据库连接测试页面-->
<view class="container">
  <view class="header">
    <text class="title">数据库连接测试</text>
    <text class="subtitle">诊断云开发数据库连接状态</text>
  </view>

  <!-- 状态概览 -->
  <view class="status-overview">
    <view class="status-item">
      <text class="status-label">云开发初始化</text>
      <text class="status-value {{cloudInitStatus === '正常' ? 'success' : cloudInitStatus === '失败' ? 'error' : 'pending'}}">
        {{cloudInitStatus}}
      </text>
    </view>
    <view class="status-item">
      <text class="status-label">数据库连接</text>
      <text class="status-value {{databaseStatus === '正常' ? 'success' : databaseStatus === '失败' ? 'error' : 'pending'}}">
        {{databaseStatus}}
      </text>
    </view>
    <view class="status-item">
      <text class="status-label">云函数调用</text>
      <text class="status-value {{cloudFunctionStatus === '正常' ? 'success' : cloudFunctionStatus === '失败' ? 'error' : 'pending'}}">
        {{cloudFunctionStatus}}
      </text>
    </view>
    <view class="status-item">
      <text class="status-label">数据库权限</text>
      <text class="status-value {{permissionStatus === '正常' ? 'success' : permissionStatus === '失败' ? 'error' : 'pending'}}">
        {{permissionStatus}}
      </text>
    </view>
    <view class="status-item">
      <text class="status-label">知识库记录数</text>
      <text class="status-value {{totalRecords > 0 ? 'success' : 'warning'}}">
        {{totalRecords}}
      </text>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-group">
    <button 
      class="test-button primary" 
      bindtap="startFullTest" 
      disabled="{{isLoading}}"
    >
      {{isLoading ? '测试中...' : '完整测试'}}
    </button>
    
    <button 
      class="test-button secondary" 
      bindtap="testDatabaseOnly" 
      disabled="{{isLoading}}"
    >
      数据库测试
    </button>
    
    <button 
      class="test-button secondary" 
      bindtap="testCloudFunctionOnly" 
      disabled="{{isLoading}}"
    >
      云函数测试
    </button>
    
    <button 
      class="test-button clear" 
      bindtap="clearLogs" 
      disabled="{{isLoading}}"
    >
      清空日志
    </button>
  </view>

  <!-- 测试日志 -->
  <view class="log-container">
    <view class="log-header">
      <text class="log-title">测试日志</text>
      <text class="log-count">{{testResults.length}} 条记录</text>
    </view>
    
    <scroll-view class="log-content" scroll-y="true" scroll-top="{{999999}}">
      <view 
        wx:for="{{testResults}}" 
        wx:key="index" 
        class="log-item {{item.type}}"
      >
        <text class="log-time">{{item.time}}</text>
        <text class="log-message">{{item.message}}</text>
      </view>
      
      <view wx:if="{{testResults.length === 0}}" class="log-empty">
        暂无测试日志
      </view>
    </scroll-view>
  </view>

  <!-- 加载指示器 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">测试进行中...</text>
    </view>
  </view>
</view>
